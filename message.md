feat: implement dynamic result determination in PGN conversion

## ChessKit PGNParser Enhancement
- Implement TODO feature in `convert(game:)` function for automatic result determination
- When result tag is empty, automatically call `determineGameResult(for: game)` to analyze final position
- Create dynamic result tag with determined outcome (checkmate, stalemate, or ongoing)
- Maintain backward compatibility with existing non-empty result tags

## Technical Implementation
- Add conditional logic to check if `game.tags.result.isEmpty`
- Create new `Game.Tag` instance with "Result" name when needed
- Set `wrappedValue` to result from `determineGameResult` function
- Seamlessly integrate with existing tag processing array

## Debug Enhancement
- Add temporary debug logging in GameSession for PGN comparison during import operations

This enhancement ensures PGN exports always include proper result tags, automatically analyzing game positions when result information is missing, improving PGN completeness and standard compliance.