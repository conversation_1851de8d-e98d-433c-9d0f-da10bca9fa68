//
//  InteractiveMoveNotationView.swift
//  MacChessBase
//
//  Created on 2025/6/2.
//

import SwiftUI
import ChessKit
import AppKit

// MARK: - Constants
fileprivate struct NotationConstants {
    /// Unified chess notation font size
    static let fontSize: CGFloat = 14
    
    // MARK: - Collapse Button Configuration
    /// Collapse button character spacing for expanding click area (precise float control)
    static let collapseButtonKernSpacing: CGFloat = 8.0
    
    /// Collapse button left and right padding (precise float control)
    static let collapseButtonPadding: CGFloat = 2.0
}

// MARK: - Custom Attribute Key
fileprivate extension NSAttributedString.Key {
    /// A custom key to embed `MoveTree.MoveIndex` directly into an attributed string.
    static let chessMoveIndex = NSAttributedString.Key("chessMoveIndex")
    /// A custom key to mark the currently active move for easy lookup and styling.
    static let isCurrentMove = NSAttributedString.Key("isCurrentMove")
    /// A custom key to mark collapse/expand buttons.
    static let collapseButton = NSAttributedString.Key("collapseButton")
    /// A custom key to embed the variation root index for collapse buttons.
    static let variationRootIndex = NSAttributedString.Key("variationRootIndex")
    /// A custom key to mark move comment text for editing.
    static let moveCommentText = NSAttributedString.Key("moveCommentText")
}

// MARK: - Main SwiftUI View
/// A view that displays interactive chess move notation using a high-performance NSTextView,
/// supporting both left-click navigation and right-click context menus.
struct InteractiveMoveNotationView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    @State private var contextMenuMoveIndex: MoveTree.MoveIndex?
    @State private var collapsedVariations: Set<MoveTree.MoveIndex> = []
    
    // Add a unique identifier to ensure view independence
    private let viewId = UUID()

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            headerView
                .padding(.bottom, 8)
            
            // PGN Metadata display
            PGNMetadataDisplayView(viewModel: viewModel)
                .padding(.top, 8)
                .padding(.bottom, 8)

            MoveNotationTextViewRepresentable(
                viewModel: viewModel,
                collapsedVariations: collapsedVariations,
                onMoveClick: { moveIndex in
                    viewModel.goToMove(at: moveIndex)
                },
                onMoveRightClick: { moveIndex, _ in
                    // Set context menu index first to avoid race condition
                    contextMenuMoveIndex = moveIndex
                    viewModel.goToMove(at: moveIndex)
                },
                onCollapseToggle: { variationRootIndex in
                    if collapsedVariations.contains(variationRootIndex) {
                        collapsedVariations.remove(variationRootIndex)
                    } else {
                        collapsedVariations.insert(variationRootIndex)
                    }
                },
                onCommentEdit: { moveIndex in
                    // Handle comment editing
                    handleCommentEdit(at: moveIndex)
                }
            )
            // Provide a flexible frame to prevent the view from collapsing to zero height.
            .frame(minHeight: 100, idealHeight: 300, maxHeight: .infinity)
            .contextMenu {
                contextMenuItems
            }
        }
        .padding(.top, 8)
        .frame(maxWidth: .infinity)
        .id(viewId) // Ensure each view instance is unique
        .background(
            // Invisible buttons for keyboard shortcuts
            VStack {
                Button("", action: collapseAllVariations)
                    .keyboardShortcut("w", modifiers: .command)
                    .opacity(0)
                
                Button("", action: expandAllVariations)
                    .keyboardShortcut("u", modifiers: .command)
                    .opacity(0)
            }
            .allowsHitTesting(false)
        )
    }

    /// The header containing the title and navigation buttons.
    private var headerView: some View {
        HStack {
            Text("Game Notation")
                .font(.headline)
            
            // Global collapse and expand buttons
            HStack(spacing: 4) {
                Button(action: collapseAllVariations) {
                    Image(systemName: "minus.circle")
                        .foregroundColor(.blue)
                }
                .buttonStyle(BorderlessButtonStyle())
                .help("Collapse all variations (⌘W)")
                
                Button(action: expandAllVariations) {
                    Image(systemName: "plus.circle")
                        .foregroundColor(.blue)
                }
                .buttonStyle(BorderlessButtonStyle())
                .help("Expand all variations (⌘U)")
            }
            
            Spacer()

            HStack(spacing: 8) {
                Button(action: { viewModel.goToStart() }) { Image(systemName: "backward.end") }
                    .disabled(!viewModel.canGoToPreviousMove)
                    .help("Go to start (⌘←)")

                Button(action: { viewModel.goToPreviousMove() }) { Image(systemName: "backward") }
                    .disabled(!viewModel.canGoToPreviousMove)
                    .help("Previous move (←)")

                Button(action: { viewModel.goToNextMove() }) { Image(systemName: "forward") }
                    .disabled(!viewModel.canGoToNextMove)
                    .help("Next move (→)")

                Button(action: { viewModel.goToEnd() }) { Image(systemName: "forward.end") }
                    .disabled(!viewModel.canGoToNextMove)
                    .help("Go to end (⌘→)")
            }
            .buttonStyle(BorderlessButtonStyle())
        }
        .padding(.horizontal, 16)
    }

    /// The context menu items displayed on right-click.
    @ViewBuilder
    private var contextMenuItems: some View {
        if let moveIndex = contextMenuMoveIndex {
            MoveEditMenuView(viewModel: viewModel, moveIndex: moveIndex)
        }
    }
    
    /// Collapse all first-level variations
    private func collapseAllVariations() {
        let firstLevelVariations = getFirstLevelVariationRootIndices()
        collapsedVariations = Set(firstLevelVariations)
    }
    
    /// Expand all variations
    private func expandAllVariations() {
        collapsedVariations.removeAll()
    }
    
    /// Get all first-level variation root indices (moves that start variations adjacent to main line)
    private func getFirstLevelVariationRootIndices() -> [MoveTree.MoveIndex] {
        var firstLevelVariations: [MoveTree.MoveIndex] = []
        let moves = viewModel.cachedMoves
        var variationDepth = 0
        
        for (index, moveDisplayItem) in moves.enumerated() {
            switch moveDisplayItem.pgnElement {
            case .variationStart:
                variationDepth += 1
                
                // Only consider first-level variations (depth == 1)
                if variationDepth == 1 {
                    // Look ahead to find the first move in this variation
                    if index + 1 < moves.count,
                       case .move(_, let nextMoveIndex) = moves[index + 1].pgnElement {
                        // Check if this variation has subsequent moves (making it collapsible)
                        if let session = viewModel.session, session.game.moves.nextIndex(currentIndex: nextMoveIndex) != nil {
                            firstLevelVariations.append(nextMoveIndex)
                        }
                    }
                }
                
            case .variationEnd:
                variationDepth -= 1
                
            default:
                break
            }
        }
        
        return firstLevelVariations
    }
    
    /// Handle comment editing for a specific move
    private func handleCommentEdit(at moveIndex: MoveTree.MoveIndex) {
        // Get current comment text
        let currentText = viewModel.getMoveCommentText(at: moveIndex)
        
        // Create and show an alert for text input
        let alert = NSAlert()
        alert.messageText = "Edit Move Comment"
        alert.informativeText = "Enter the comment for this move:"
        alert.addButton(withTitle: "Save")
        alert.addButton(withTitle: "Cancel")
        
        // Create text field for input
        let textField = NSTextField(frame: NSRect(x: 0, y: 0, width: 300, height: 24))
        textField.stringValue = currentText
        textField.placeholderString = "Enter comment text..."
        alert.accessoryView = textField
        
        // Make text field first responder
        alert.window.initialFirstResponder = textField
        
        // Show the alert
        let response = alert.runModal()
        
        if response == .alertFirstButtonReturn {
            // User clicked Save
            let newText = textField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
            viewModel.setMoveCommentText(newText, at: moveIndex)
        }
    }
}

// MARK: - NSViewRepresentable Implementation
fileprivate struct MoveNotationTextViewRepresentable: NSViewRepresentable {
    @ObservedObject var viewModel: ChessGameViewModel
    let collapsedVariations: Set<MoveTree.MoveIndex>
    
    var onMoveClick: (MoveTree.MoveIndex) -> Void
    var onMoveRightClick: (MoveTree.MoveIndex, CGPoint) -> Void
    var onCollapseToggle: (MoveTree.MoveIndex) -> Void
    var onCommentEdit: (MoveTree.MoveIndex) -> Void
    
    // Add unique identifier for this representable instance
    private let representableId = UUID()

    func makeNSView(context: Context) -> NSScrollView {
        let textView = MoveClickableTextView(frame: .zero)
        textView.isEditable = false
        textView.isSelectable = false // Disable text selection to prevent iBeam cursor
        textView.drawsBackground = false
        textView.textContainerInset = NSSize(width: 5, height: 5) // Reduced padding for compactness
        textView.delegate = context.coordinator
        textView.coordinator = context.coordinator

        // Configure text view for vertical resizing and line wrapping
        textView.isVerticallyResizable = true
        textView.isHorizontallyResizable = false
        textView.autoresizingMask = [.width]
        textView.textContainer?.widthTracksTextView = true

        let scrollView = NSScrollView()
        scrollView.hasVerticalScroller = true
        scrollView.documentView = textView
        
        return scrollView
    }

    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? MoveClickableTextView else { return }
        
        // Ensure the coordinator references are properly set on each update
        textView.coordinator = context.coordinator
        context.coordinator.textView = textView
        
        // Ensure the text container tracks the scroll view's width
        let containerWidth = nsView.contentSize.width
        if textView.textContainer?.containerSize.width != containerWidth {
            textView.textContainer?.containerSize = NSSize(width: containerWidth, height: CGFloat.greatestFiniteMagnitude)
        }

        // Check if only the current move has changed without content changes
        if viewModel.hasOnlyCurrentMoveChanged {
            // Only update highlighting, don't rebuild the entire string
            updateHighlightingOnly(textView: textView)
        } else {
            // Content has changed, need to rebuild the entire attributed string
            let newAttributedString = buildAttributedNotation()
            textView.textStorage?.setAttributedString(newAttributedString)
        }
        
        context.coordinator.scrollToCurrentMove()
    }
    
    /// Updates only the highlighting attributes without changing the text layout
    private func updateHighlightingOnly(textView: MoveClickableTextView) {
        guard let textStorage = textView.textStorage else { return }
        guard let session = viewModel.session else { return }
        
        let fullRange = NSRange(location: 0, length: textStorage.length)
        let mainVariationIndices = session.game.moves.allMainVariationIndices
        
        // Check if game has variations
        let hasVariation = session.game.moves.hasVariation
        
        // Perform all attribute updates in a single batch to avoid layout changes
        textStorage.beginEditing()
        
        // First, remove all current move highlighting
        textStorage.removeAttribute(.isCurrentMove, range: fullRange)
        textStorage.removeAttribute(.backgroundColor, range: fullRange)
        
        // Reset colors to default for all moves and apply current move highlighting
        textStorage.enumerateAttribute(.chessMoveIndex, in: fullRange, options: []) { value, range, _ in
            guard let moveIndex = value as? MoveTree.MoveIndex else { return }
            
            let isOnMainLine = mainVariationIndices.contains(moveIndex)
            let isCurrentMove = viewModel.currentMoveIndex == moveIndex
            let paragraphStyle = textStorage.attribute(.paragraphStyle, at: range.location, effectiveRange: nil) as! NSParagraphStyle
            
            // 使用统一的样式函数获取属性
            let attributes = getAttributesForMove(
                moveIndex: moveIndex,
                isCurrentMove: isCurrentMove,
                isOnMainLine: isOnMainLine,
                hasVariation: hasVariation,
                paragraphStyle: paragraphStyle
            )
            
            // 应用所有属性
            for (key, value) in attributes {
                textStorage.addAttribute(key, value: value, range: range)
            }
        }
        
        textStorage.endEditing()
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }
    
    /// Constructs the `NSAttributedString` for the entire game notation.
    private func buildAttributedNotation() -> NSAttributedString {
        let result = NSMutableAttributedString()
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 1 // Further reduced line spacing for vertical compactness
        paragraphStyle.alignment = .left // Use left alignment to prevent uneven horizontal spacing
        
        let spaceAttributes: [NSAttributedString.Key: Any] = [
            .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular),
            .paragraphStyle: paragraphStyle,
            .kern: -2.5 // Apply negative kerning to tighten the space between moves
        ]

        guard let session = viewModel.session else { return NSAttributedString() }
        
        let moves = viewModel.cachedMoves
        let mainVariationIndices = session.game.moves.allMainVariationIndices
        
        // Check if game has variations
        let hasVariation = session.game.moves.hasVariation
        
        // Track variation depth and line breaks for advanced UI
        var variationDepth = 0
        var needsLineBreak = false
        var justEndedVariation = false
        
        // collapse variabls
        var isCollapse = false
        var isCollapseRoot = false
        
        for (index, moveDisplayItem) in moves.enumerated() {
            var attributes: [NSAttributedString.Key: Any] = [
                .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular),
                .paragraphStyle: paragraphStyle
            ]
            
            var text = ""

            switch moveDisplayItem.pgnElement {
            case .move(let move, let moveIndex):
                // Skip if we're inside a collapsed variation
                if isCollapse && !isCollapseRoot {
                    continue
                }
                
                if let metaMove = move.metaMove {
                    // For moves, use the embedded number and move text (without comment)
                    if let moveNumber = moveDisplayItem.moveNumber, let isWhiteMove = moveDisplayItem.isWhiteMove {
                        
                        if isWhiteMove {
                            text = "\(moveNumber).\(metaMove.displayDescription)"
                        } else {
                            // Use ellipsis character for more compact black move notation
                            text = "\(moveNumber)…\(metaMove.displayDescription)"
                        }
                    } else {
                        // No number to display, just the move
                        text = "\(metaMove.displayDescription)"
                    }
                }
                
                attributes[.chessMoveIndex] = moveIndex // Embed move index
                
                // Determine if this move is on the main line
                let isOnMainLine = mainVariationIndices.contains(moveIndex)
                
                // Check if we need a line break: when we just ended a variation and now encounter a main line move
                if justEndedVariation && isOnMainLine && variationDepth == 0 {
                    needsLineBreak = true
                }
                
                // Check if we need a line break before the move
                if needsLineBreak {
                    result.append(NSAttributedString(string: "\n", attributes: [
                        .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular),
                        .paragraphStyle: paragraphStyle
                    ]))
                    needsLineBreak = false
                }
                
                // 使用统一的样式函数获取招法属性
                let isCurrentMove = viewModel.currentMoveIndex == moveIndex
                let moveAttributes = getAttributesForMove(
                    moveIndex: moveIndex,
                    isCurrentMove: isCurrentMove,
                    isOnMainLine: isOnMainLine,
                    hasVariation: hasVariation,
                    paragraphStyle: paragraphStyle
                )
                
                // 合并招法属性到现有的基础属性中
                for (key, value) in moveAttributes {
                    attributes[key] = value
                }
                
                justEndedVariation = false // Reset after processing a move
                isCollapseRoot = false
                
            case .variationStart:
                variationDepth += 1
                if isCollapse {
                    continue
                }
                
                // Add line break before first-level variations (variationDepth == 0)
                // but only if we're not at the beginning of the notation
                if variationDepth == 1 && !result.string.isEmpty {
                    result.append(NSAttributedString(string: "\n", attributes: [
                        .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular),
                        .paragraphStyle: paragraphStyle
                    ]))
                }
                
                
                // Look ahead to find the first move in this variation
                guard index + 1 < moves.count else {
                    continue    // should not happen
                }
                
                guard case .move(_, let nextMoveIndex) = moves[index + 1].pgnElement else {
                    continue    // should not happen
                }
                
                if collapsedVariations.contains(nextMoveIndex) {
                    isCollapse = true
                }
                
                // Add collapse button if needed
                if session.game.moves.nextIndex(currentIndex: nextMoveIndex) != nil {
                    let collapseSymbol = isCollapse ? "▶" : "▼"
                    
                    // 使用kern和baselineOffset来精确控制按钮的大小和间距（浮点数控制）
                    let collapseAttributes: [NSAttributedString.Key: Any] = [
                        .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .semibold),
                        .foregroundColor: NSColor.systemBlue,
                        .paragraphStyle: paragraphStyle,
                        .collapseButton: true,
                        .variationRootIndex: nextMoveIndex,
                        .kern: NotationConstants.collapseButtonKernSpacing, // 使用浮点数精确控制字符间距
                        .expansion: NotationConstants.collapseButtonPadding / 10.0 // 使用expansion属性增加字符宽度
                    ]
                    
                    result.append(NSAttributedString(string: collapseSymbol, attributes: collapseAttributes))
                }
                
                text = variationDepth == 1 ? "[" : "("
                attributes[.foregroundColor] = NSColor.gray
                justEndedVariation = false
                isCollapseRoot = true
                
            case .variationEnd:
                variationDepth -= 1
                
                if isCollapse && variationDepth != 0 {
                    continue
                }
                
                text = variationDepth == 0 ? "]" : ")"
                attributes[.foregroundColor] = NSColor.gray
                
                // Check if we're exiting a collapsed variation
                if variationDepth == 0 {
                    // Mark that we just ended a first-level variation
                    justEndedVariation = true
                }
                
                isCollapse = false
                                
            case .whiteNumber, .blackNumber:
                // These should not appear in our filtered data, but handle gracefully
                continue
                
            case nil:
                // Handle case where pgnElement is nil (shouldn't happen with current logic)
                continue
            }
            
            // Append the element's text
            result.append(NSAttributedString(string: text, attributes: attributes))
            
            // Special handling for moves: add visual annotation icon and comment text if present
            if case .move(let move, let moveIndex) = moveDisplayItem.pgnElement {
                let hasVisualAnnotations = !move.positionComment.visualAnnotations.isEmpty
                let hasTextComment = !move.positionComment.text.isEmpty
                
                if hasVisualAnnotations || hasTextComment {
                    // Add a space before annotations/comments
                    if index != 0 {
                        result.append(NSAttributedString(string: " ", attributes: [
                            .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular),
                            .paragraphStyle: paragraphStyle
                        ]))
                    }
                    
                    // Add visual annotation icon if present
                    if hasVisualAnnotations {
                        let iconAttributes: [NSAttributedString.Key: Any] = [
                            .font: NSFont.systemFont(ofSize: NotationConstants.fontSize, weight: .regular),
                            .foregroundColor: NSColor.systemBlue, // Blue color for visual annotation icon
                            .paragraphStyle: paragraphStyle
                        ]
                        
                        // Use a palette/drawing icon to indicate visual annotations
                        // Unicode: 🎨 (artist palette) or 🖍️ (crayon) or 📐 (triangular ruler)
                        result.append(NSAttributedString(string: "🎨", attributes: iconAttributes))
                        
                        // Add a small space after the icon if there's also text comment
                        if hasTextComment {
                            result.append(NSAttributedString(string: " ", attributes: [
                                .font: NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular),
                                .paragraphStyle: paragraphStyle
                            ]))
                        }
                    }
                    
                    // Add the comment text if present
                    if hasTextComment {
                        let commentAttributes: [NSAttributedString.Key: Any] = [
                            .font: NSFont.systemFont(ofSize: NotationConstants.fontSize, weight: .regular), // Different font from moves
                            .foregroundColor: NSColor.systemPurple, // Purple color for comments
                            .paragraphStyle: paragraphStyle,
                            .chessMoveIndex: moveIndex, // Allow clicking to edit
                            .moveCommentText: true // Mark as comment text
                        ]
                        
                        result.append(NSAttributedString(string: move.positionComment.text, attributes: commentAttributes))
                    }
                    
                    if index == 0 {
                        result.append(NSAttributedString(string: " ", attributes: spaceAttributes))
                    }
                }
            }
            
            guard !isCollapse else { continue }
                
            // Add a space intelligently if not the last element
            if index < moves.count - 1 {
                let currentElement = moveDisplayItem.pgnElement
                let nextElement = moves[index + 1].pgnElement

                var addSpace = true
                
                // Rule 1: Don't add space for headNode
                if index == 0 {
                    addSpace = false
                }

                // Rule 2: Don't add space after an opening parenthesis
                if case .variationStart = currentElement {
                    addSpace = false
                }

                // Rule 3: Don't add space before a closing parenthesis
                if case .variationEnd = nextElement {
                    addSpace = false
                }
                
                // Rule 4: Don't add space before a line break (will be handled by needsLineBreak)
                if case .variationStart = nextElement, variationDepth == 0 {
                    addSpace = false
                }

                if addSpace {
                    // Use a generic attribute set for the space to avoid carrying over color
                    result.append(NSAttributedString(string: " ", attributes: spaceAttributes))
                }
            }
        }
        return result
    }

    // MARK: - Coordinator
    class Coordinator: NSObject, NSTextViewDelegate {
        var parent: MoveNotationTextViewRepresentable
        weak var textView: MoveClickableTextView?

        init(parent: MoveNotationTextViewRepresentable) {
            self.parent = parent
        }
        
        /// Called by the custom text view on a right-click event.
        func handleRightClick(at point: NSPoint) {
            guard let textView = self.textView,
                  let layoutManager = textView.layoutManager,
                  let textContainer = textView.textContainer else { return }

            let charIndex = layoutManager.characterIndex(for: point, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)

            // Check if the game has no moves (only headNode)
            let hasNoMoves = parent.viewModel.session?.game.moves.allIndices.count == 1

            if hasNoMoves {
                // For empty games, allow right-click anywhere in the view to edit beginning comment
                let globalPoint = textView.convert(point, to: nil)
                parent.onMoveRightClick(MoveTree.minimumIndex, globalPoint)
                return
            }

            // Check if the click is within the actual text bounds
            guard let textStorage = textView.textStorage, charIndex < textStorage.length else {
                // Click is outside text area, don't handle right-click
                return
            }

            // Additional check: ensure the click point is actually within the glyph bounds
            let glyphRange = layoutManager.glyphRange(forCharacterRange: NSRange(location: charIndex, length: 1), actualCharacterRange: nil)
            let glyphRect = layoutManager.boundingRect(forGlyphRange: glyphRange, in: textContainer)

            // If click point is significantly outside the glyph bounds, don't treat it as a text click
            if !glyphRect.insetBy(dx: -10, dy: -5).contains(point) {
                return
            }

            if let moveIndex = textStorage.attribute(.chessMoveIndex, at: charIndex, effectiveRange: nil) as? MoveTree.MoveIndex {
                let globalPoint = textView.convert(point, to: nil)
                parent.onMoveRightClick(moveIndex, globalPoint)
            }
        }
        
        /// Called by the custom text view on a collapse button click.
        func handleCollapseToggle(at point: NSPoint) {
            guard let textView = self.textView,
                  let layoutManager = textView.layoutManager,
                  let textContainer = textView.textContainer else { return }

            let charIndex = layoutManager.characterIndex(for: point, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)

            if charIndex < textView.textStorage?.length ?? 0 {
                if let variationRootIndex = textView.textStorage?.attribute(.variationRootIndex, at: charIndex, effectiveRange: nil) as? MoveTree.MoveIndex {
                    parent.onCollapseToggle(variationRootIndex)
                }
            }
        }
        
        /// Called by the custom text view on a comment text click for editing.
        func handleCommentEdit(at moveIndex: MoveTree.MoveIndex) {
            parent.onCommentEdit(moveIndex)
        }
        
        /// Scrolls the text view to make the current move visible.
        func scrollToCurrentMove() {
            guard let textView = self.textView, let textStorage = textView.textStorage else { return }
            let fullRange = NSRange(location: 0, length: textStorage.length)
            
            textStorage.enumerateAttribute(.isCurrentMove, in: fullRange, options: []) { (value, range, stop) in
                if let isCurrent = value as? Bool, isCurrent {
                    DispatchQueue.main.async {
                        textView.scrollRangeToVisible(range)
                    }
                    stop.pointee = true
                }
            }
        }
    }
}

// MARK: - Style Configuration Extension
fileprivate extension MoveNotationTextViewRepresentable {
    /// 统一的招法样式配置方法，用于处理所有招法的文本颜色和字体样式
    func getAttributesForMove(
        moveIndex: MoveTree.MoveIndex,
        isCurrentMove: Bool,
        isOnMainLine: Bool,
        hasVariation: Bool,
        paragraphStyle: NSParagraphStyle
    ) -> [NSAttributedString.Key: Any] {
        
        var attributes: [NSAttributedString.Key: Any] = [
            .paragraphStyle: paragraphStyle,
            .chessMoveIndex: moveIndex
        ]
        
        if isCurrentMove {
            // 当前招法：高亮显示
            attributes[.isCurrentMove] = true
            attributes[.backgroundColor] = NSColor.controlAccentColor
            attributes[.foregroundColor] = NSColor.white
            attributes[.font] = NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular)
        } else {
            // 非当前招法：根据是否在主线和是否有变化设置样式
            if isOnMainLine {
                if hasVariation {
                    // 游戏有变化：主线招法使用粗体
                    attributes[.foregroundColor] = NSColor.labelColor
                    attributes[.font] = NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .semibold)
                } else {
                    // 无变化：主线和变化之间的中等样式
                    attributes[.foregroundColor] = NSColor.labelColor.withAlphaComponent(0.7)
                    attributes[.font] = NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .medium)
                }
            } else {
                // 变化招法：较浅的颜色和常规字重
                attributes[.foregroundColor] = NSColor.labelColor.withAlphaComponent(0.65)
                attributes[.font] = NSFont.monospacedSystemFont(ofSize: NotationConstants.fontSize, weight: .regular)
            }
        }
        
        return attributes
    }
}

// MARK: - Custom NSTextView for Click Handling
fileprivate class MoveClickableTextView: NSTextView {
    weak var coordinator: MoveNotationTextViewRepresentable.Coordinator?

    override func resetCursorRects() {
        super.resetCursorRects()
        // Set the cursor to the arrow for the entire view bounds,
        // overriding the default iBeam cursor of NSTextView
        addCursorRect(bounds, cursor: .arrow)
    }

    /// Overridden to intercept right-clicks for our context menu logic.
    override func menu(for event: NSEvent) -> NSMenu? {
        let locationInView = self.convert(event.locationInWindow, from: nil)
        coordinator?.handleRightClick(at: locationInView)
        // Return nil to prevent the default AppKit menu from appearing,
        // allowing SwiftUI's `contextMenu` to take over.
        return nil
    }
    
    /// Overridden to handle left-clicks for move navigation and collapse button clicks.
    override func mouseDown(with event: NSEvent) {
        guard event.type == .leftMouseDown, event.clickCount == 1 else {
            super.mouseDown(with: event)
            return
        }
        
        guard let layoutManager = self.layoutManager,
              let textContainer = self.textContainer else {
            super.mouseDown(with: event)
            return
        }
        
        let point = self.convert(event.locationInWindow, from: nil)
        let charIndex = layoutManager.characterIndex(for: point, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)
        
        // Check if the click is within the actual text bounds
        guard let textStorage = self.textStorage, charIndex < textStorage.length else {
            // Click is outside text area, use default behavior
            super.mouseDown(with: event)
            return
        }
        
        // Additional check: ensure the click point is actually within the glyph bounds
        let glyphRange = layoutManager.glyphRange(forCharacterRange: NSRange(location: charIndex, length: 1), actualCharacterRange: nil)
        let glyphRect = layoutManager.boundingRect(forGlyphRange: glyphRange, in: textContainer)
        
        // If click point is significantly outside the glyph bounds, don't treat it as a text click
        if !glyphRect.insetBy(dx: -10, dy: -5).contains(point) {
            super.mouseDown(with: event)
            return
        }
        
        // Check if this is a collapse button click first
        if let isCollapseButton = textStorage.attribute(.collapseButton, at: charIndex, effectiveRange: nil) as? Bool,
           isCollapseButton {
            // Handle collapse button click through coordinator
            coordinator?.handleCollapseToggle(at: point)
            return
        }
        
        // Check for move click
        if let moveIndex = textStorage.attribute(.chessMoveIndex, at: charIndex, effectiveRange: nil) as? MoveTree.MoveIndex {
            // Check if this is a comment text click
            if let isCommentText = textStorage.attribute(.moveCommentText, at: charIndex, effectiveRange: nil) as? Bool,
               isCommentText {
                // Handle comment text click for editing
                coordinator?.handleCommentEdit(at: moveIndex)
                return
            }
            
            // Regular move click
            coordinator?.parent.onMoveClick(moveIndex)
            return
        }
        
        // If no move was clicked, fall back to default behavior (e.g., text selection).
        super.mouseDown(with: event)
    }
}

// MARK: - PGN Metadata Display View
/// A compact view to display PGN game metadata
struct PGNMetadataDisplayView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // First line: Players and result
            playersAndResultView
            
            // Second line: Event and date
            eventAndDateView
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(6)
        .font(.system(size: 13))
        .foregroundColor(.primary)
    }
    
    private var playersAndResultView: some View {
        HStack {
            Spacer()
            
            playersText
            
            if !gameResult.isEmpty {
                Text(gameResult)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
    }
    
    private var playersText: some View {
        HStack(spacing: 0) {
            let metadata = viewModel.session?.game.metadata ?? Game.Metadata()
            let whitePlayer = formatPlayerName(metadata.white, color: .white)
            let blackPlayer = formatPlayerName(metadata.black, color: .black)
            
            if !whitePlayer.name.isEmpty || !blackPlayer.name.isEmpty {
                // White player name (semibold)
                Text(whitePlayer.name.isEmpty ? "?" : whitePlayer.name)
                    .fontWeight(.semibold)
                
                // White player rating (regular)
                if !whitePlayer.rating.isEmpty {
                    Text(" \(whitePlayer.rating)")
                        .fontWeight(.regular)
                }
                
                // Dash (semibold)
                Text(" - ")
                    .fontWeight(.semibold)
                
                // Black player name (semibold)
                Text(blackPlayer.name.isEmpty ? "?" : blackPlayer.name)
                    .fontWeight(.semibold)
                
                // Black player rating (regular)
                if !blackPlayer.rating.isEmpty {
                    Text(" \(blackPlayer.rating)")
                        .fontWeight(.regular)
                }
            } else {
                Text("No player information")
                    .fontWeight(.semibold)
            }
        }
        .truncationMode(.middle)
    }
    
    private var eventAndDateView: some View {
        HStack {
            Spacer()
            if !gameEvent.isEmpty {
                Text(gameEvent)
                    .truncationMode(.tail)
            }
            
            if !gameDate.isEmpty {
                Text(gameDate)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Computed Properties
    
    /// Separates player name and rating for individual formatting
    private func formatPlayerName(_ name: String, color: Piece.Color) -> (name: String, rating: String) {
        if name.isEmpty {
            return (name: "", rating: "")
        }
        
        // Extract rating from name if it contains rating info
        // Format: "Player Name (2500)" or "Player Name"
        let ratingPattern = #"\((\d+)\)"#
        if let ratingMatch = name.range(of: ratingPattern, options: .regularExpression) {
            let rating = String(name[ratingMatch]).replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "")
            let nameWithoutRating = name.replacingOccurrences(of: ratingPattern, with: "", options: .regularExpression).trimmingCharacters(in: .whitespaces)
            return (name: nameWithoutRating, rating: rating)
        }
        
        // Check for rating in other tags
        var rating = ""
        if let session = viewModel.session {
            switch color {
            case .white: rating = session.game.tags.whiteElo
            case .black: rating = session.game.tags.blackElo
            }
        }
        
        return (name: name, rating: rating)
    }
    
    private var formattedPlayersText: String {
        let metadata = viewModel.session?.game.metadata ?? Game.Metadata()
        let whitePlayer = viewModel.formatPlayerInfo(name: metadata.white, color: .white)
        let blackPlayer = viewModel.formatPlayerInfo(name: metadata.black, color: .black)
        
        if whitePlayer.isEmpty && blackPlayer.isEmpty {
            return "No player information"
        }
        
        return "\(whitePlayer) - \(blackPlayer)"
    }
    
    private var gameResult: String {
        let result = viewModel.session?.game.metadata.result ?? ""
        return result.isEmpty || result == "*" ? "" : result
    }
    
    private var gameEvent: String {
        let event = viewModel.session?.game.metadata.event ?? ""
        return event.isEmpty ? "Casual Game" : event
    }
    
    private var gameDate: String {
        let date = viewModel.session?.game.metadata.date ?? ""
        if date.isEmpty || date.contains("?") {
            return ""
        }
        
        // Convert YYYY.MM.DD to more readable format if needed
        if date.contains(".") {
            let components = date.split(separator: ".")
            if components.count == 3 {
                let year = components[0]
                let month = components[1]
                let day = components[2]
                
                // Create a simple readable format
                return "\(day)/\(month)/\(year)"
            }
        }
        
        return date
    }
}
