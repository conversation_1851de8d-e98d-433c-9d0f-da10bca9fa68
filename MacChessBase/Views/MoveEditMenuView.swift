//
//  MoveEditMenuView.swift
//  MacChessBase
//
//  Created by Assistant on 2025/6/14.
//

import SwiftUI
import ChessKit
import AppKit

/// A context menu for editing chess moves
struct MoveEditMenuView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    let moveIndex: MoveTree.MoveIndex
    
    var body: some View {
        ZStack {
            VStack(alignment: .leading, spacing: 0) {
                // Special case for headNode (empty game)
                if moveIndex == MoveTree.minimumIndex {
                    MenuButton(
                        title: "Comment at the beginning",
                        action: { editBeginningComment() },
                        disabled: false
                    )
                } else {
                    // Delete move option
                    MenuButton(
                        title: "Delete Move",
                        action: { viewModel.deleteMove(at: moveIndex) },
                        disabled: false
                    )

                    MenuButton(
                        title: "Delete Variation",
                        action: { viewModel.deleteVariation(at: moveIndex) },
                        disabled: false
                    )

                    MenuButton(
                        title: "Delete Before Move",
                        action: { viewModel.deleteBeforeMove(at: moveIndex) },
                        disabled: moveIndex == MoveTree.minimumIndex
                    )

                    // Promote variation priority option
                    MenuButton(
                        title: "Promote",
                        action: { viewModel.promoteVariation(at: moveIndex) },
                        disabled: !viewModel.canPromoteVariation(at: moveIndex)
                    )

                    MenuButton(
                        title: "Promote to Main",
                        action: { viewModel.promoteToMainVariation(at: moveIndex) },
                        disabled: !viewModel.canPromoteVariation(at: moveIndex)
                    )

                    Divider()

                    // Comment editing option
                    MenuButton(
                        title: "Comment Before Move",
                        action: { editCommentBefore(moveIndex: moveIndex) },
                        disabled: false
                    )

                    MenuButton(
                        title: "Comment After Move",
                        action: { editCommentAfter(moveIndex: moveIndex) },
                        disabled: false
                    )

                    Divider()

                    // Move assessments submenu
                    Menu {
                        ForEach(MetaMove.moveAssessments, id: \.self) { assessment in
                            Button(assessment.assessmentDescription) {
                                viewModel.setMoveAssessment(assessment, at: moveIndex)
                            }
                        }
                    } label: {
                        HStack(spacing: 8) {
                            Text("!, ?, ...")
                                .foregroundColor(.primary)

                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            Rectangle()
                                .fill(Color.clear)
                        )
                    }
                    .menuStyle(.borderlessButton)

                    // Position assessments submenu
                    Menu {
                        ForEach(MetaMove.positionAssessments, id: \.self) { assessment in
                            Button(assessment.assessmentDescription) {
                                viewModel.setPositionAssessment(assessment, at: moveIndex)
                            }
                        }
                    } label: {
                        HStack(spacing: 8) {
                            Text("+-, =, ...")
                                .foregroundColor(.primary)

                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            Rectangle()
                                .fill(Color.clear)
                        )
                    }
                    .menuStyle(.borderlessButton)
                }

            }
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(NSColor.separatorColor), lineWidth: 1)
            )
        }
        .allowsHitTesting(true) // Ensure proper hit testing for submenus
    }
    
    // MARK: - Comment Editing Methods
    
    /// Edit the text comment for the current move
    private func editCommentAfter(moveIndex: MoveTree.MoveIndex) {
        // Get current comment text
        let currentText = viewModel.getMoveCommentText(at: moveIndex)
        
        // Create and show an alert for text input
        let alert = NSAlert()
        alert.messageText = "Edit Before Move"
        alert.informativeText = "Enter the comment for this move:"
        alert.addButton(withTitle: "Save")
        alert.addButton(withTitle: "Cancel")
        
        // Create text field for input
        let textField = NSTextField(frame: NSRect(x: 0, y: 0, width: 300, height: 24))
        textField.stringValue = currentText
        textField.placeholderString = "Enter comment text..."
        alert.accessoryView = textField
        
        // Make text field first responder
        alert.window.initialFirstResponder = textField
        
        // Show the alert
        let response = alert.runModal()
        
        if response == .alertFirstButtonReturn {
            // User clicked Save
            let newText = textField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
            viewModel.setMoveCommentText(newText, at: moveIndex)
        }
    }
    
    /// Remove the text comment for the current move
    private func editCommentBefore(moveIndex: MoveTree.MoveIndex) {
        guard let session = viewModel.session else { return }
        let previousIndex = session.game.moves.previousIndex(currentIndex: moveIndex)!
        editCommentAfter(moveIndex: previousIndex)
    }

    /// Edit the beginning comment for empty games (headNode)
    @MainActor
    private func editBeginningComment() {
        guard let session = viewModel.session else { return }

        // Get current comment text from headNode
        let currentText = session.game.moves.getNodeMove(index: MoveTree.minimumIndex)?.positionComment.text ?? ""

        // Create and show an alert for text input
        let alert = NSAlert()
        alert.messageText = "Edit Beginning Comment"
        alert.informativeText = "Enter the comment for the beginning position:"
        alert.addButton(withTitle: "Save")
        alert.addButton(withTitle: "Cancel")

        // Create text field for input
        let textField = NSTextField(frame: NSRect(x: 0, y: 0, width: 300, height: 24))
        textField.stringValue = currentText
        textField.placeholderString = "Enter comment text..."
        alert.accessoryView = textField

        // Make text field first responder
        alert.window.initialFirstResponder = textField

        // Show the alert
        let response = alert.runModal()

        if response == .alertFirstButtonReturn {
            // User clicked Save
            let newText = textField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
            viewModel.setMoveCommentText(newText, at: MoveTree.minimumIndex)
        }
    }
}

/// A button for the edit menu
struct MenuButton: View {
    let title: String
    let action: () -> Void
    let disabled: Bool
    
    // Convenience initializer for backward compatibility
    init(title: String, action: @escaping () -> Void, disabled: Bool) {
        self.title = title
        self.action = action
        self.disabled = disabled
    }
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Text(title)
                    .foregroundColor(disabled ? .gray : .primary)
                    
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Rectangle()
                    .fill(isHovered ? Color(NSColor.controlAccentColor).opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            isHovered = hovering
        }
        .disabled(disabled)
    }
}
